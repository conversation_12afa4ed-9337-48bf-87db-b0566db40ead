import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { z } from "zod";
export class MCPTestClient {
    client = null;
    config;
    constructor(config) {
        this.config = config;
    }
    async init() {
        const transport = new StdioClientTransport({
            command: this.config.serverCommand,
            args: this.config.serverArgs,
            env: this.config.serverEnv,
        });
        this.client = new Client({
            name: "test-client",
            version: "1.0.0",
        }, {
            capabilities: {},
        });
        await this.client.connect(transport);
    }
    async listTools() {
        if (!this.client)
            throw new Error("Client not initialized");
        const schema = z.object({ tools: z.array(z.any()) });
        const response = await this.client.request({ method: "tools/list" }, schema);
        return response.tools;
    }
    async callTool(toolName, args) {
        if (!this.client)
            throw new Error("Client not initialized");
        const schema = z.object({ content: z.array(z.any()) });
        return await this.client.request({
            method: "tools/call",
            params: {
                name: toolName,
                arguments: args,
            },
        }, schema);
    }
    async assertToolCall(toolName, args, assertion) {
        const result = await this.callTool(toolName, args);
        await assertion(result);
    }
    async cleanup() {
        if (this.client) {
            this.client = null;
        }
    }
}
//# sourceMappingURL=MCPTestClient.js.map