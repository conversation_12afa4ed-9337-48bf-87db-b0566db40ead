import { Tool } from "@modelcontextprotocol/sdk/types.js";
export type ToolResult = any;
export declare class MCPTestClient {
    private client;
    private config;
    constructor(config: {
        serverCommand: string;
        serverArgs: string[];
        serverEnv?: Record<string, string>;
    });
    init(): Promise<void>;
    listTools(): Promise<Tool[]>;
    callTool(toolName: string, args: Record<string, unknown>): Promise<ToolResult>;
    assertToolCall(toolName: string, args: Record<string, unknown>, assertion: (result: ToolResult) => void | Promise<void>): Promise<void>;
    cleanup(): Promise<void>;
}
