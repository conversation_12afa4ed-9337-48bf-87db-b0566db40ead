/**
 * GitHub API 相关工具函数
 */
import { GitHubRepoInfo, GitHubFileContent } from '../types/github.js';
/**
 * 获取 GitHub 仓库内容
 * @param repoInfo - 仓库信息
 * @param path - 文件或目录路径
 * @returns 文件内容数组
 */
export declare function getGitHubRepoContents(repoInfo: GitHubRepoInfo, path?: string): Promise<GitHubFileContent[]>;
/**
 * 下载单个文件内容
 * @param downloadUrl - 文件下载 URL
 * @returns 文件内容
 */
export declare function downloadFileContent(downloadUrl: string): Promise<string>;
/**
 * 递归获取目录下的所有文件
 * @param repoInfo - 仓库信息
 * @param dirPath - 目录路径
 * @returns 所有文件内容
 */
export declare function getAllFilesInDirectory(repoInfo: GitHubRepoInfo, dirPath?: string): Promise<GitHubFileContent[]>;
/**
 * 检查仓库是否存在
 * @param repoInfo - 仓库信息
 * @returns 仓库是否存在
 */
export declare function checkRepoExists(repoInfo: GitHubRepoInfo): Promise<boolean>;
/**
 * 使用 git clone 下载 GitHub 仓库
 * @param repoInfo - GitHub 仓库信息
 * @param targetDir - 目标目录
 * @returns 下载的文件列表
 */
export declare function cloneGitHubRepo(repoInfo: GitHubRepoInfo, targetDir: string): Promise<GitHubFileContent[]>;
//# sourceMappingURL=github.d.ts.map