/**
 * GitHub API 相关类型定义
 */
export interface GitHubRepoInfo {
    owner: string;
    repo: string;
}
export interface GitHubFileContent {
    name: string;
    path: string;
    content: string;
    type: 'file' | 'dir';
    download_url?: string;
}
export interface GitHubApiResponse {
    name: string;
    path: string;
    sha: string;
    size: number;
    url: string;
    html_url: string;
    git_url: string;
    download_url: string | null;
    type: 'file' | 'dir';
    content?: string;
    encoding?: string;
}
export interface ClaudeCodeConfig {
    subAgents: SubAgent[];
    slashCommands: SlashCommand[];
}
export interface SubAgent {
    name: string;
    description: string;
    tools?: string[];
    systemPrompt: string;
    filePath: string;
}
export interface SlashCommand {
    name: string;
    description: string;
    allowedTools?: string[];
    argumentHint?: string;
    model?: string;
    content: string;
    filePath: string;
}
export interface MarkdownFile {
    name: string;
    path: string;
    content: string;
    frontmatter?: Record<string, any>;
    markdownContent: string;
}
//# sourceMappingURL=github.d.ts.map