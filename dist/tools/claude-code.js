/**
 * Claude Code MCP Tools - Register individual sub-agents and commands as MCP tools
 */
import { z } from 'zod';
import path from 'path';
import { parseGitHubRepo, validateTargetDir } from '../utils/validation.js';
import { cloneGitHubRepo } from '../utils/github.js';
import { getFilesInDir, fileExists, readFile } from '../utils/file.js';
import { processMarkdownFile } from '../utils/markdown.js';
/**
 * Check and register tools from local .xclaude directory
 * @param server - MCP server instance
 * @param targetDir - Target directory path
 * @returns Whether local content was found and registered
 */
async function checkAndRegisterLocalTools(server, targetDir) {
    try {
        const xclaudeExists = await fileExists(targetDir);
        if (!xclaudeExists) {
            return false;
        }
        let hasContent = false;
        // Check for agents directory
        const agentsDir = path.join(targetDir, 'agents');
        const agentsExists = await fileExists(agentsDir);
        if (agentsExists) {
            const agentFiles = await getFilesInDir(agentsDir);
            for (const filePath of agentFiles) {
                if (filePath.endsWith('.md')) {
                    await registerAgentFromFile(server, filePath);
                    hasContent = true;
                }
            }
        }
        // Check for commands directory
        const commandsDir = path.join(targetDir, 'commands');
        const commandsExists = await fileExists(commandsDir);
        if (commandsExists) {
            const commandFiles = await getFilesInDir(commandsDir);
            for (const filePath of commandFiles) {
                if (filePath.endsWith('.md')) {
                    await registerCommandFromFile(server, filePath);
                    hasContent = true;
                }
            }
        }
        return hasContent;
    }
    catch (error) {
        console.warn(`Failed to check local .xclaude directory: ${error}`);
        return false;
    }
}
/**
 * Download from GitHub and register tools
 * @param server - MCP server instance
 * @param githubRepo - GitHub repository
 * @param targetDir - Target directory
 */
async function downloadAndRegisterFromGitHub(server, githubRepo, targetDir) {
    // Validate input parameters
    const repoInfo = parseGitHubRepo(githubRepo);
    const safeTargetDir = validateTargetDir(targetDir);
    // Download repository using git clone
    const files = await cloneGitHubRepo(repoInfo, safeTargetDir);
    if (files.length === 0) {
        throw new Error(`No markdown files found in repository ${repoInfo.owner}/${repoInfo.repo}`);
    }
    console.log(`Downloaded ${files.length} files from ${repoInfo.owner}/${repoInfo.repo}`);
    // Register tools from downloaded files
    await registerToolsFromFiles(server, files);
}
/**
 * Register tools from downloaded files
 * @param server - MCP server instance
 * @param files - Downloaded files
 */
async function registerToolsFromFiles(server, files) {
    const markdownFiles = files.filter(f => f.name.endsWith('.md'));
    for (const file of markdownFiles) {
        const processedFile = processMarkdownFile(file);
        // Determine if it's an agent or command based on path
        if (file.path.includes('agents/') || file.path.includes('agents\\')) {
            await registerAgentFromProcessedFile(server, processedFile);
        }
        else if (file.path.includes('commands/') || file.path.includes('commands\\')) {
            await registerCommandFromProcessedFile(server, processedFile);
        }
    }
}
/**
 * Register agent from file path
 * @param server - MCP server instance
 * @param filePath - File path
 */
async function registerAgentFromFile(server, filePath) {
    try {
        const content = await readFile(filePath);
        const processedFile = processMarkdownFile({
            name: path.basename(filePath),
            path: filePath,
            content
        });
        await registerAgentFromProcessedFile(server, processedFile);
    }
    catch (error) {
        console.warn(`Failed to register agent from ${filePath}: ${error}`);
    }
}
/**
 * Register command from file path
 * @param server - MCP server instance
 * @param filePath - File path
 */
async function registerCommandFromFile(server, filePath) {
    try {
        const content = await readFile(filePath);
        const processedFile = processMarkdownFile({
            name: path.basename(filePath),
            path: filePath,
            content
        });
        await registerCommandFromProcessedFile(server, processedFile);
    }
    catch (error) {
        console.warn(`Failed to register command from ${filePath}: ${error}`);
    }
}
/**
 * Register agent from processed markdown file
 * @param server - MCP server instance
 * @param file - Processed markdown file
 */
async function registerAgentFromProcessedFile(server, file) {
    const { frontmatter, markdownContent } = file;
    if (!frontmatter || !frontmatter.name || !frontmatter.description) {
        console.warn(`Agent file ${file.path} missing required frontmatter fields`);
        return;
    }
    const toolName = `${frontmatter.name}-agent`;
    server.registerTool(toolName, {
        title: `Agent: ${frontmatter.name}`,
        description: frontmatter.description,
        inputSchema: {
            task: z.string().describe('Task description for the agent')
        }
    }, async (args) => {
        const { task } = args;
        return {
            content: [{
                    type: 'text',
                    text: `Executing agent ${frontmatter.name} with task: ${task}\n\nSystem Prompt:\n${markdownContent}`
                }]
        };
    });
}
/**
 * Register command from processed markdown file
 * @param server - MCP server instance
 * @param file - Processed markdown file
 */
async function registerCommandFromProcessedFile(server, file) {
    const { frontmatter, markdownContent } = file;
    const name = file.name.replace('.md', '');
    const description = (frontmatter?.description) || markdownContent.split('\n')[0] || `Command: ${name}`;
    const toolName = `${name}-command`;
    server.registerTool(toolName, {
        title: `Command: ${name}`,
        description,
        inputSchema: {
            arguments: z.string().optional().describe('Arguments for the command (replaces $ARGUMENTS in content)')
        }
    }, async (args) => {
        const { arguments: cmdArgs = '' } = args;
        // Replace $ARGUMENTS placeholder with actual arguments
        const processedContent = markdownContent.replace(/\$ARGUMENTS/g, cmdArgs);
        return {
            content: [{
                    type: 'text',
                    text: `Executing command ${name}${cmdArgs ? ` with arguments: ${cmdArgs}` : ''}\n\nContent:\n${processedContent}`
                }]
        };
    });
}
/**
 * Register claude-code tools with optional GitHub repository configuration
 * @param server - MCP server instance
 * @param githubRepo - Optional GitHub repository to download from if no local content
 */
export async function registerClaudeCodeTool(server, githubRepo) {
    // 注册状态工具
    server.registerTool('claude-code-status', {
        title: 'Claude Code Status',
        description: 'Get the status of claude-code tools',
        inputSchema: {}
    }, async () => {
        const localExists = await fileExists('.xclaude');
        let agentsCount = 0;
        let commandsCount = 0;
        if (localExists) {
            try {
                if (await fileExists('.xclaude/agents')) {
                    agentsCount = (await getFilesInDir('.xclaude/agents')).filter(f => f.endsWith('.md')).length;
                }
                if (await fileExists('.xclaude/commands')) {
                    commandsCount = (await getFilesInDir('.xclaude/commands')).filter(f => f.endsWith('.md')).length;
                }
            }
            catch (error) {
                // 忽略文件系统错误
            }
        }
        return {
            content: [{
                    type: 'text',
                    text: `Claude Code MCP Tool Status:
- Configured GitHub Repository: ${githubRepo || 'Not configured'}
- Local .xclaude directory: ${localExists ? 'Found' : 'Not found'}
- Registered agents: ${agentsCount}
- Registered commands: ${commandsCount}
- Total tools available: ${agentsCount + commandsCount + 2}

💡 Tip: Use 'init-xclaude' tool to download and register tools from GitHub repository.`
                }]
        };
    });
    // 注册初始化工具
    server.registerTool('init-xclaude', {
        title: 'Initialize XClaude Tools',
        description: 'Download and register claude-code agents and commands from GitHub repository. This is the only way to initialize tools from GitHub.',
        inputSchema: {
            githubRepo: z.string().describe('GitHub repository URL or owner/repo format (e.g., "cexll/myclaude" or "https://github.com/cexll/myclaude")')
        }
    }, async (args) => {
        try {
            const { githubRepo: repoArg } = args;
            const targetDir = '.xclaude';
            // 下载并注册工具
            await downloadAndRegisterFromGitHub(server, repoArg, targetDir);
            // 获取注册的工具数量
            let agentsCount = 0;
            let commandsCount = 0;
            try {
                if (await fileExists('.xclaude/agents')) {
                    agentsCount = (await getFilesInDir('.xclaude/agents')).filter(f => f.endsWith('.md')).length;
                }
                if (await fileExists('.xclaude/commands')) {
                    commandsCount = (await getFilesInDir('.xclaude/commands')).filter(f => f.endsWith('.md')).length;
                }
            }
            catch (error) {
                // 使用默认值
            }
            return {
                content: [{
                        type: 'text',
                        text: `✅ Successfully initialized XClaude tools from ${repoArg}

📊 Summary:
- Downloaded repository: ${repoArg}
- Agents registered: ${agentsCount}
- Commands registered: ${commandsCount}
- Total new tools: ${agentsCount + commandsCount}

🎯 Usage:
- Use "claude-code-status" to check current status
- Individual agents are available as "{name}-agent"
- Individual commands are available as "{filename}-command"

Example:
- use bmad-architect-agent with task="Design a system"
- use bugfix-command with arguments="Fix login issue"`
                    }]
            };
        }
        catch (error) {
            return {
                content: [{
                        type: 'text',
                        text: `❌ Failed to initialize XClaude tools: ${error}

💡 Tips:
- Check if the GitHub repository exists and is accessible
- Ensure the repository has 'agents/' and/or 'commands/' directories
- Verify the repository URL format (e.g., "owner/repo" or full URL)`
                    }],
                isError: true
            };
        }
    });
    // 只注册本地已存在的工具，不自动下载
    await registerLocalTools(server);
}
/**
 * Register tools from local .xclaude directory if it exists
 * @param server - MCP server instance
 */
async function registerLocalTools(server) {
    const targetDir = '.xclaude';
    try {
        const hasLocalContent = await checkAndRegisterLocalTools(server, targetDir);
        if (hasLocalContent) {
            console.log('Registered tools from local .xclaude directory');
        }
        else {
            console.log('No local .xclaude directory found. Use init-xclaude tool to download from GitHub.');
        }
    }
    catch (error) {
        console.error(`Failed to register local tools: ${error}`);
    }
}
//# sourceMappingURL=claude-code.js.map