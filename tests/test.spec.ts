/**
 * Claude Code MCP 单元测试
 */

import { describe, beforeAll, afterAll, test, expect } from 'vitest';
import fs from 'fs-extra';
import path from 'path';
import { parseGitHubRepo } from '../src/utils/validation.js';
import { processMarkdownFile } from '../src/utils/markdown.js';

describe('Claude Code MCP Unit Tests', () => {
  const testXclaudeDir = '.xclaude-test';

  beforeAll(async () => {
    // 创建测试用的 .xclaude 目录
    await setupTestXclaudeDirectory();
  });

  afterAll(async () => {
    // 清理测试目录
    await fs.remove(testXclaudeDir);
  });

  async function setupTestXclaudeDirectory() {
    // 创建测试目录结构
    await fs.ensureDir(path.join(testXclaudeDir, 'agents'));
    await fs.ensureDir(path.join(testXclaudeDir, 'commands'));

    // 创建测试 agent
    const agentContent = `---
name: test-reviewer
description: Test code review agent
tools: Read, Grep
---

You are a test code reviewer. When given a task, analyze the code and provide feedback.

Test instructions:
1. Review the provided code
2. Check for basic issues
3. Provide constructive feedback
`;

    await fs.writeFile(
      path.join(testXclaudeDir, 'agents', 'test-reviewer.md'),
      agentContent
    );

    // 创建测试 command
    const commandContent = `# Test Command

This is a test command that processes arguments.

## Usage

Execute with: $ARGUMENTS

## Instructions

1. Process the input arguments
2. Return formatted output
3. Complete the task
`;

    await fs.writeFile(
      path.join(testXclaudeDir, 'commands', 'test-command.md'),
      commandContent
    );
  }

  test('should parse GitHub repository URLs correctly', () => {
    // 测试 GitHub URL 解析
    const testCases = [
      {
        input: 'cexll/myclaude',
        expected: { owner: 'cexll', repo: 'myclaude' }
      },
      {
        input: 'https://github.com/cexll/myclaude',
        expected: { owner: 'cexll', repo: 'myclaude' }
      },
      {
        input: 'https://github.com/owner/repo.git',
        expected: { owner: 'owner', repo: 'repo' }
      }
    ];

    testCases.forEach(({ input, expected }) => {
      const result = parseGitHubRepo(input);
      expect(result.owner).toBe(expected.owner);
      expect(result.repo).toBe(expected.repo);
    });
  });

  test('should process markdown files with frontmatter correctly', () => {
    // 测试 Markdown 文件处理
    const testFile = {
      name: 'test-agent.md',
      path: 'subagents/test-agent.md',
      content: `---
name: test-agent
description: Test agent for testing
tools: Read, Write
---

This is the system prompt for the test agent.

Instructions:
1. Do something
2. Do something else
`
    };

    const processed = processMarkdownFile(testFile);

    expect(processed.frontmatter).toBeDefined();
    expect(processed.frontmatter?.name).toBe('test-agent');
    expect(processed.frontmatter?.description).toBe('Test agent for testing');
    expect(processed.markdownContent).toContain('This is the system prompt');
    expect(processed.markdownContent).toContain('Instructions:');
  });

  test('should handle markdown files without frontmatter', () => {
    // 测试没有 frontmatter 的 Markdown 文件
    const testFile = {
      name: 'simple-command.md',
      path: 'commands/simple-command.md',
      content: `# Simple Command

This is a simple command without frontmatter.

Execute with: $ARGUMENTS

## Steps
1. Process input
2. Return result
`
    };

    const processed = processMarkdownFile(testFile);

    expect(processed.frontmatter).toEqual({});
    expect(processed.markdownContent).toContain('# Simple Command');
    expect(processed.markdownContent).toContain('$ARGUMENTS');
  });

  test('should convert .claude references to .xclaude', () => {
    // 测试 .claude 到 .xclaude 的转换
    const testFile = {
      name: 'test.md',
      path: 'test.md',
      content: `# Test File

This file references .claude directory and .claude files.

See .claude/subagents/ for more information.
Check the .claude configuration.
## Validation Criteria

### 1. **Template Structure Compliance**
- **Load and compare against template**: Use get-content script to read the requirements template:

~~~bash
# Windows:
claude-code-spec-workflow get-content "C:\path\to\project\.claude\templates\requirements-template.md"

# macOS/Linux:
claude-code-spec-workflow get-content "/path/to/project/.claude/templates/requirements-template.md"

- **Section validation**: Ensure all required template sections are present and non-empty
- **Format compliance**: Verify document follows exact template structure and formatting
- **Section order**: Check that sections appear in the correct template order
- **Missing sections**: Identify any template sections that are missing or incomplete
~~~
`
    };

    const processed = processMarkdownFile(testFile);

    expect(processed.content).toContain('.xclaude directory');
    expect(processed.content).toContain('.xclaude files');
    expect(processed.content).toContain('.xclaude/subagents/');
    expect(processed.content).toContain('.xclaude configuration');
    expect(processed.content).toContain('/path/to/project/.xclaude/');
    expect(processed.content).not.toContain('.claude');
  });

  test('should create test directory structure correctly', async () => {
    // 测试目录创建
    await setupTestXclaudeDirectory();

    // 验证目录结构
    expect(await fs.pathExists(testXclaudeDir)).toBe(true);
    expect(await fs.pathExists(path.join(testXclaudeDir, 'agents'))).toBe(true);
    expect(await fs.pathExists(path.join(testXclaudeDir, 'commands'))).toBe(true);

    // 验证文件存在
    expect(await fs.pathExists(path.join(testXclaudeDir, 'agents', 'test-reviewer.md'))).toBe(true);
    expect(await fs.pathExists(path.join(testXclaudeDir, 'commands', 'test-command.md'))).toBe(true);

    // 验证文件内容
    const agentContent = await fs.readFile(path.join(testXclaudeDir, 'agents', 'test-reviewer.md'), 'utf-8');
    expect(agentContent).toContain('name: test-reviewer');
    expect(agentContent).toContain('You are a test code reviewer');

    const commandContent = await fs.readFile(path.join(testXclaudeDir, 'commands', 'test-command.md'), 'utf-8');
    expect(commandContent).toContain('# Test Command');
    expect(commandContent).toContain('$ARGUMENTS');
  });
});
