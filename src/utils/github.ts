/**
 * GitHub API 相关工具函数
 */

import fetch from 'node-fetch';
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs-extra';
import path from 'path';
import { GitHubRepoInfo, GitHubApiResponse, GitHubFileContent } from '../types/github.js';

const execAsync = promisify(exec);

/**
 * GitHub API 基础 URL
 */
const GITHUB_API_BASE = 'https://api.github.com';

/**
 * 获取 GitHub 仓库内容
 * @param repoInfo - 仓库信息
 * @param path - 文件或目录路径
 * @returns 文件内容数组
 */
export async function getGitHubRepoContents(
  repoInfo: GitHubRepoInfo,
  path = ''
): Promise<GitHubFileContent[]> {
  const { owner, repo } = repoInfo;
  const pathSegment = path ? `/${path}` : '';
  const url = `${GITHUB_API_BASE}/repos/${owner}/${repo}/contents${pathSegment}`;

  try {
    const response = await fetch(url);

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error(`Repository ${owner}/${repo} not found or path ${path} does not exist`);
      }
      throw new Error(`GitHub API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json() as GitHubApiResponse | GitHubApiResponse[];
    const items = Array.isArray(data) ? data : [data];

    return items.map(item => ({
      name: item.name,
      path: item.path,
      content: item.content ? Buffer.from(item.content, 'base64').toString('utf-8') : '',
      type: item.type,
      download_url: item.download_url || undefined
    }));
  } catch (error) {
    throw new Error(`Failed to fetch GitHub repository contents: ${error}`);
  }
}

/**
 * 下载单个文件内容
 * @param downloadUrl - 文件下载 URL
 * @returns 文件内容
 */
export async function downloadFileContent(downloadUrl: string): Promise<string> {
  try {
    const response = await fetch(downloadUrl);

    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.status} ${response.statusText}`);
    }

    return await response.text();
  } catch (error) {
    throw new Error(`Failed to download file content: ${error}`);
  }
}

/**
 * 递归获取目录下的所有文件
 * @param repoInfo - 仓库信息
 * @param dirPath - 目录路径
 * @returns 所有文件内容
 */
export async function getAllFilesInDirectory(
  repoInfo: GitHubRepoInfo,
  dirPath = ''
): Promise<GitHubFileContent[]> {
  const allFiles: GitHubFileContent[] = [];

  try {
    const contents = await getGitHubRepoContents(repoInfo, dirPath);

    for (const item of contents) {
      if (item.type === 'file') {
        // 如果是文件，下载内容
        if (item.download_url) {
          item.content = await downloadFileContent(item.download_url);
        }
        allFiles.push(item);
      } else if (item.type === 'dir') {
        // 如果是目录，递归获取子文件
        const subFiles = await getAllFilesInDirectory(repoInfo, item.path);
        allFiles.push(...subFiles);
      }
    }
  } catch (error) {
    throw new Error(`Failed to get all files in directory ${dirPath}: ${error}`);
  }

  return allFiles;
}

/**
 * 检查仓库是否存在
 * @param repoInfo - 仓库信息
 * @returns 仓库是否存在
 */
export async function checkRepoExists(repoInfo: GitHubRepoInfo): Promise<boolean> {
  const { owner, repo } = repoInfo;
  const url = `${GITHUB_API_BASE}/repos/${owner}/${repo}`;

  try {
    const response = await fetch(url);
    return response.ok;
  } catch {
    return false;
  }
}

/**
 * 使用 git clone 下载 GitHub 仓库
 * @param repoInfo - GitHub 仓库信息
 * @param targetDir - 目标目录
 * @returns 下载的文件列表
 */
export async function cloneGitHubRepo(repoInfo: GitHubRepoInfo, targetDir: string): Promise<GitHubFileContent[]> {
  const { owner, repo } = repoInfo;
  const repoUrl = `https://github.com/${owner}/${repo}.git`;
  const tempDir = `${targetDir}-temp`;

  try {
    // 确保目标目录存在
    await fs.ensureDir(path.dirname(targetDir));

    // 清理可能存在的临时目录
    if (await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }

    // 使用 git clone --depth=1 下载仓库（不指定分支，使用默认分支）
    const cloneCommand = `git clone --depth=1 ${repoUrl} ${tempDir}`;
    console.log(`Cloning repository: ${cloneCommand}`);

    await execAsync(cloneCommand);

    // 移除 .git 目录
    const gitDir = path.join(tempDir, '.git');
    if (await fs.pathExists(gitDir)) {
      await fs.remove(gitDir);
    }

    // 移除不需要的文件
    const unnecessaryFiles = ['.gitignore', 'README.md', 'README-zh.md', 'LICENSE', 'BMAD-README.md', 'BMAD-PILOT-USER-GUIDE.md'];
    for (const file of unnecessaryFiles) {
      const filePath = path.join(tempDir, file);
      if (await fs.pathExists(filePath)) {
        await fs.remove(filePath);
      }
    }

    // 移除不需要的目录（只保留 agents, commands）
    const allItems = await fs.readdir(tempDir);
    const keepDirs = ['agents', 'commands'];

    for (const item of allItems) {
      const itemPath = path.join(tempDir, item);
      const stat = await fs.stat(itemPath);

      if (stat.isDirectory() && !keepDirs.includes(item)) {
        await fs.remove(itemPath);
      } else if (stat.isFile() && !item.endsWith('.md')) {
        // 移除非 markdown 文件
        await fs.remove(itemPath);
      }
    }

    // 移动到最终目录
    if (await fs.pathExists(targetDir)) {
      await fs.remove(targetDir);
    }
    await fs.move(tempDir, targetDir);

    // 收集所有 markdown 文件
    const files: GitHubFileContent[] = [];
    await collectMarkdownFiles(targetDir, '', files);

    return files;
  } catch (error) {
    // 清理临时目录
    if (await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
    throw new Error(`Failed to clone repository ${owner}/${repo}: ${error}`);
  }
}

/**
 * 递归收集 markdown 文件
 */
async function collectMarkdownFiles(baseDir: string, relativePath: string, files: GitHubFileContent[]): Promise<void> {
  const currentDir = path.join(baseDir, relativePath);

  if (!(await fs.pathExists(currentDir))) {
    return;
  }

  const items = await fs.readdir(currentDir);

  for (const item of items) {
    const itemPath = path.join(currentDir, item);
    const itemRelativePath = relativePath ? path.join(relativePath, item) : item;
    const stat = await fs.stat(itemPath);

    if (stat.isDirectory()) {
      await collectMarkdownFiles(baseDir, itemRelativePath, files);
    } else if (stat.isFile() && item.endsWith('.md')) {
      const content = await fs.readFile(itemPath, 'utf-8');
      files.push({
        name: item,
        path: itemRelativePath.replace(/\\/g, '/'), // 确保使用正斜杠
        content,
        type: 'file'
      });
    }
  }
}
